import { useState, useCallback, useEffect } from 'react';
import { _addNote, _getNotes } from 'controllers/notes/notes_controllers';
import { _getExecutionsByLeadId } from 'controllers/leads/leads_controller';
import { getResponseValue } from 'util/validate';
import { getProfile } from 'util/auth';

export function useNotes(lead) {
  const [notes, setNotes] = useState();
  const [history, setHistory] = useState();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchNotes = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      if (lead?.notes) {
        setNotes(lead.notes);
      } else {
        const res = await _getNotes('lead', lead.id);
        let notesTemp = await getResponseValue(res);
        const messages = await _getExecutionsByLeadId(lead.school_id, lead.id);
        setHistory(messages);
        // let allNotes = [...notesTemp, ...messages];
        // allNotes.sort((a, b) => (a.created_at > b.created_at ? -1 : a.created_at < b.created_at ? 1 : 0));
        setNotes(notesTemp);
      }
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  }, [lead]);

  useEffect(() => {
    fetchNotes();
  }, [lead]);

  const createNode = useCallback(
    async ({ leadId, note }) => {
      const user = getProfile();
      setLoading(true);
      try {
        const payload = new FormData();
        payload.append('object_id', leadId);
        payload.append('object_type', 'lead');
        payload.append('user_id', user.email);
        payload.append('note', note);

        const response = await _addNote(payload);
        if (response.ok) {
          await fetchNotes();
        }
      } catch (err) {
        console.error(err);
        setError(err);
      } finally {
        setLoading(false);
      }
    },
    [lead, fetchNotes]
  );

  return { notes, history, createNode, fetchNotes, loading };
}
