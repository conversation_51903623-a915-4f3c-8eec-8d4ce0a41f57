/* @import '../../../../../sass/variables';
@import '../../../../../sass/color-palette'; */

.messageModal {
  .ant-modal-content {
    padding: 0;
  }

  .ant-modal-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #f0f0f0;
  }

  .ant-modal-body {
    padding: 0;
  }
}

.viewModeContainer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem 0;

  .viewModeLabel {
    font-size: 14px;
    color: #666;
  }

  .viewModeButton {
    border: 1px solid #d9d9d9;
    background: #f5f5f5;
    color: #666;

    &:hover {
      border-color: #40a9ff;
      color: #40a9ff;
    }
  }
}

.tabContainer {
  padding: 1rem 1.5rem 0;

  .segmentedTabs {
    background: #f5f5f5;
    border-radius: 8px;
    padding: 4px;

    .ant-segmented-item {
      border-radius: 6px;

      &.ant-segmented-item-selected {
        background: white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .tabOption {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;

    span {
      font-weight: 500;
    }
  }
}

.senderInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;

  .sendingAs {
    font-weight: 500;
    color: #333;
  }

  .senderBadge {
    background: #e6f7ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
    border-radius: 4px;
    padding: 2px 8px;
    font-size: 12px;
  }

  .scholaId {
    font-weight: 500;
    color: #333;
  }

  .scholaIdLink {
    color: #1890ff;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.conversationHistory {
  max-height: 400px;
  overflow-y: auto;
  padding: 1rem 1.5rem;
  background: #fafafa;

  .messageItem {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #f0f0f0;

    &.sent {
      border-left: 4px solid #1890ff;
    }

    &.received {
      border-left: 4px solid #52c41a;
    }

    .messageHeader {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-bottom: 0.5rem;

      .messageIcon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #f0f0f0;
        color: #666;
      }

      .messageInfo {
        flex: 1;

        .senderName {
          font-weight: 600;
          color: #333;
          margin-right: 0.5rem;
        }

        .timestamp {
          font-size: 12px;
          color: #999;
        }
      }

      .statusBadge {
        font-size: 12px;
        padding: 2px 8px;
        border-radius: 4px;

        &.sent {
          background: #e6f7ff;
          color: #1890ff;
          border: 1px solid #91d5ff;
        }

        &.received {
          background: #f6ffed;
          color: #52c41a;
          border: 1px solid #b7eb8f;
        }
      }
    }

    .emailSubject {
      margin-bottom: 0.5rem;
      font-size: 14px;
      color: #666;
    }

    .messageContent {
      color: #333;
      line-height: 1.5;
      margin-bottom: 0.5rem;
    }

    .attachments {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 12px;
      color: #666;

      .attachment {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        background: #f0f0f0;
        padding: 2px 6px;
        border-radius: 4px;
      }
    }
  }
}

.messageComposer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #f0f0f0;
  background: white;

  .subjectInput {
    margin-bottom: 1rem;
  }

  .textareaContainer {
    position: relative;
    margin-bottom: 1rem;

    .messageTextarea {
      width: 100%;
      min-height: 100px;
      padding: 0.75rem;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      resize: vertical;
      font-family: inherit;
      font-size: 14px;
      line-height: 1.5;

      &:focus {
        outline: none;
        border-color: #40a9ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }

      &::placeholder {
        color: #bfbfbf;
      }
    }

    .emojiButton {
      position: absolute;
      bottom: 0.75rem;
      right: 0.75rem;
    }
  }

  .attachmentsList {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;

    .attachmentItem {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      background: #f0f0f0;
      padding: 0.5rem;
      border-radius: 4px;
      font-size: 12px;

      span {
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .actionButtons {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .leftActions {
      .attachButton {
        color: #666;

        &:hover {
          color: #1890ff;
        }
      }
    }

    .sendButton {
      background: #00a693;
      border-color: #00a693;

      &:hover {
        background: #008a7a;
        border-color: #008a7a;
      }

      &:disabled {
        background: #f5f5f5;
        border-color: #d9d9d9;
        color: #bfbfbf;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .messageModal {
    .ant-modal {
      margin: 0;
      max-width: 100vw;
      top: 0;
    }

    .ant-modal-content {
      border-radius: 0;
    }
  }

  .conversationHistory {
    max-height: 300px;
  }

  .senderInfo {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .actionButtons {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;

    .sendButton {
      width: 100%;
    }
  }
}
