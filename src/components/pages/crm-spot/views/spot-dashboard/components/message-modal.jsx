import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { Modal, Button, Input, Segmented, Badge, Tooltip } from 'antd';
import { MessageOutlined, MailOutlined, PaperClipOutlined, SendOutlined, SmileOutlined } from '@ant-design/icons';
import { useNotification } from 'components/pages/crm-spot/layouts/crm-spot-layout';
import { _sendEmailMessageToLead, _sendSmsMessageToLead } from 'controllers/messages/messages_controllers';
import styles from './message-modal.module.scss';

export const MessageModal = ({ open, toggleMessage }) => {
  const { selectedLead } = useSelector((state) => state.spot);

  // Tab state
  const [activeTab, setActiveTab] = useState('text');

  // Message states
  const [smsMessage, setSmsMessage] = useState('');
  const [emailSubject, setEmailSubject] = useState('');
  const [emailMessage, setEmailMessage] = useState('');

  // Attachment states
  const [smsAttachments, setSmsAttachments] = useState([]);
  const [emailAttachments, setEmailAttachments] = useState([]);

  const { openNotification } = useNotification();

  // Mock conversation data
  const mockConversations = [
    {
      id: 1,
      type: 'text',
      sender: 'Schola Team',
      timestamp: 'May 20, 2024 at 10:15 AM',
      content: 'Just a reminder that we have a tour scheduled for tomorrow at 2pm.',
      direction: 'sent',
      attachments: ['brochure.pdf'],
    },
    {
      id: 2,
      type: 'text',
      sender: `Test Lead ${selectedLead?.id || '12370'}`,
      timestamp: 'May 20, 2024 at 10:30 AM',
      content: "Thanks for the reminder. I'll be there.",
      direction: 'received',
    },
    {
      id: 3,
      type: 'text',
      sender: 'Schola Team',
      timestamp: 'May 22, 2024 at 9:30 AM',
      content: 'Would you be available for a quick call tomorrow to discuss the STEM program in more detail?',
      direction: 'sent',
    },
    {
      id: 4,
      type: 'email',
      sender: 'Schola Team',
      timestamp: 'May 15, 2024 at 2:30 PM',
      content: 'Hello, I wanted to follow up on your interest in our schools.',
      subject: 'Follow-up on your interest',
      direction: 'sent',
    },
    {
      id: 5,
      type: 'email',
      sender: `Test Lead ${selectedLead?.id || '12370'}`,
      timestamp: 'May 15, 2024 at 3:45 PM',
      content: "Thank you for your help. I'm interested in scheduling a tour.",
      subject: 'Re: Follow-up on your interest',
      direction: 'received',
    },
    {
      id: 6,
      type: 'email',
      sender: 'Schola Team',
      timestamp: 'May 21, 2024 at 9:00 AM',
      content: '',
      subject: 'School Programs Information',
      direction: 'sent',
    },
  ];

  // Handle attachment upload
  const handleAttachmentUpload = (event, type) => {
    const files = Array.from(event.target.files);
    if (type === 'sms') {
      setSmsAttachments((prev) => [...prev, ...files]);
    } else {
      setEmailAttachments((prev) => [...prev, ...files]);
    }
  };

  // Remove attachment
  const removeAttachment = (index, type) => {
    if (type === 'sms') {
      setSmsAttachments((prev) => prev.filter((_, i) => i !== index));
    } else {
      setEmailAttachments((prev) => prev.filter((_, i) => i !== index));
    }
  };

  // Handle send message
  const sendMessageToLead = async () => {
    try {
      const messageType = activeTab === 'text' ? 'sms' : 'email';
      const message = activeTab === 'text' ? smsMessage : emailMessage;
      const subject = activeTab === 'email' ? emailSubject : '';

      const response = await triggerMessageToLead({
        schoolId: selectedLead.school_id,
        messageType,
        leadId: selectedLead.id,
        message,
        subject,
      });

      // Clear form after sending
      if (activeTab === 'text') {
        setSmsMessage('');
        setSmsAttachments([]);
      } else {
        setEmailMessage('');
        setEmailSubject('');
        setEmailAttachments([]);
      }

      if (response.success) {
        openNotification({
          type: 'success',
          message: `${messageType === 'sms' ? 'Text' : 'Email'} sent successfully`,
          description: `Message sent to lead ${selectedLead.id}`,
        });
        return;
      }

      openNotification({
        type: 'warning',
        message: `${messageType === 'sms' ? 'Text' : 'Email'} failed to send`,
        description: `Message failed`,
      });
    } catch (error) {
      openNotification({
        type: 'warning',
        message: `Message failed to send`,
        description: `An error occurred while sending the message`,
      });
    }
  };

  // Filter conversations by active tab
  const filteredConversations = mockConversations.filter((conv) => conv.type === activeTab);

  return (
    <Modal
      open={open}
      onCancel={toggleMessage}
      title={`Conversations with Test Lead ${selectedLead?.id || '12370'}`}
      footer={null}
      width={900}
      className={styles.messageModal}>
      {/* View mode toggle */}
      <div className={styles.viewModeContainer}>
        <span className={styles.viewModeLabel}>View mode:</span>
        <Button size="small" className={styles.viewModeButton}>
          Separate
        </Button>
      </div>

      {/* Tab Navigation */}
      <div className={styles.tabContainer}>
        <Segmented
          options={[
            {
              label: (
                <div className={styles.tabOption}>
                  <MessageOutlined />
                  <span>Text</span>
                </div>
              ),
              value: 'text',
            },
            {
              label: (
                <div className={styles.tabOption}>
                  <MailOutlined />
                  <span>Email</span>
                </div>
              ),
              value: 'email',
            },
          ]}
          value={activeTab}
          onChange={setActiveTab}
          className={styles.segmentedTabs}
          block
        />
      </div>

      {/* Sender Info */}
      <div className={styles.senderInfo}>
        <span className={styles.sendingAs}>Sending as:</span>
        <Badge color="#1890ff" text="Schola Team" className={styles.senderBadge} />
        <span className={styles.scholaId}>Schola ID:</span>
        <a href="#" className={styles.scholaIdLink}>
          S12345
        </a>
      </div>

      {/* Conversation History */}
      <div className={styles.conversationHistory}>
        {filteredConversations.map((conversation) => (
          <div key={conversation.id} className={`${styles.messageItem} ${styles[conversation.direction]}`}>
            <div className={styles.messageHeader}>
              <div className={styles.messageIcon}>{activeTab === 'text' ? <MessageOutlined /> : <MailOutlined />}</div>
              <div className={styles.messageInfo}>
                <span className={styles.senderName}>{conversation.sender}</span>
                <span className={styles.timestamp}>{conversation.timestamp}</span>
              </div>
              <Badge
                text={conversation.direction === 'sent' ? 'Sent' : 'Received'}
                className={`${styles.statusBadge} ${styles[conversation.direction]}`}
              />
            </div>

            {activeTab === 'email' && conversation.subject && (
              <div className={styles.emailSubject}>
                <strong>Subject:</strong> {conversation.subject}
              </div>
            )}

            <div className={styles.messageContent}>{conversation.content}</div>

            {conversation.attachments && (
              <div className={styles.attachments}>
                <PaperClipOutlined /> Attachments ({conversation.attachments.length})
                {conversation.attachments.map((attachment, index) => (
                  <div key={index} className={styles.attachment}>
                    <PaperClipOutlined />
                    <span>{attachment}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Message Composer */}
      <div className={styles.messageComposer}>
        {activeTab === 'email' && (
          <Input
            placeholder="Subject"
            value={emailSubject}
            onChange={(e) => setEmailSubject(e.target.value)}
            className={styles.subjectInput}
          />
        )}

        <div className={styles.textareaContainer}>
          <textarea
            placeholder={activeTab === 'text' ? 'Compose your text message...' : 'Compose your email...'}
            value={activeTab === 'text' ? smsMessage : emailMessage}
            onChange={(e) => (activeTab === 'text' ? setSmsMessage(e.target.value) : setEmailMessage(e.target.value))}
            className={styles.messageTextarea}
          />

          <div className={styles.emojiButton}>
            <Tooltip title="Add emoji">
              <Button type="text" icon={<SmileOutlined />} />
            </Tooltip>
          </div>
        </div>

        {/* Attachments Display */}
        {((activeTab === 'text' && smsAttachments.length > 0) ||
          (activeTab === 'email' && emailAttachments.length > 0)) && (
          <div className={styles.attachmentsList}>
            {(activeTab === 'text' ? smsAttachments : emailAttachments).map((file, index) => (
              <div key={index} className={styles.attachmentItem}>
                <PaperClipOutlined />
                <span>{file.name}</span>
                <Button
                  type="text"
                  size="small"
                  onClick={() => removeAttachment(index, activeTab === 'text' ? 'sms' : 'email')}>
                  ×
                </Button>
              </div>
            ))}
          </div>
        )}

        {/* Action Buttons */}
        <div className={styles.actionButtons}>
          <div className={styles.leftActions}>
            <input
              type="file"
              id="attachment-upload"
              style={{ display: 'none' }}
              onChange={(e) => handleAttachmentUpload(e, activeTab === 'text' ? 'sms' : 'email')}
              multiple
            />
            <Button
              type="text"
              icon={<PaperClipOutlined />}
              onClick={() => document.getElementById('attachment-upload').click()}
              className={styles.attachButton}>
              Attach {activeTab === 'text' ? 'Image' : 'File'}
            </Button>
          </div>

          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={sendMessageToLead}
            className={styles.sendButton}
            disabled={activeTab === 'text' ? !smsMessage.trim() : !emailMessage.trim()}>
            Send {activeTab === 'text' ? 'Text' : 'Email'}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

const triggerMessageToLead = async ({ schoolId, messageType, leadId, message, subject }) => {
  try {
    const formattedBody = message.replace(/\r?\n/g, '<br />');

    let result;
    if (messageType === 'email') {
      result = await _sendEmailMessageToLead(schoolId, leadId, subject, formattedBody);
    } else {
      result = await _sendSmsMessageToLead(schoolId, leadId, message);
    }

    if (result && result.success) {
      return { success: true, data: result };
    }
    return { success: false, error: 'Could not send message' };
  } catch (error) {
    return { success: false, error };
  }
};
