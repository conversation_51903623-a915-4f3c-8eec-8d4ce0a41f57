import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Modal, Button, Input, Flex, Segmented } from 'antd';
import { useSelector } from 'react-redux';
import { useNotes } from 'components/pages/crm-spot/hooks/useNotes';
import styles from './lead-history-modal.module.scss';
import { formatDate } from 'components/pages/crm-spot/utils/format-date';

const { TextArea } = Input;

export const LeadHistoryModal = ({ open, onClose }) => {
  const { selectedLead } = useSelector((state) => state.spot);
  const [activeTab, setActiveTab] = useState('notes');
  const [newNote, setNewNote] = useState('');

  const { notes, history, createNode, loading } = useNotes(selectedLead);

  const handleAddNote = async () => {
    if (newNote.trim()) {
      await createNode({ leadId: selectedLead.id, note: newNote });
      setNewNote('');
    }
  };

  const leadName = selectedLead ? `${selectedLead.parent_first_name} ${selectedLead.parent_last_name}` : '';

  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null}
      width={800}
      className={styles.leadHistoryModal}
      title={`Lead History - ${leadName} - ID: ${selectedLead?.id}`}
      centered>
      <Flex vertical>
        <div className={styles.tabsContainer}>
          <Segmented
            options={[
              { label: 'Notes', value: 'notes' },
              { label: 'History', value: 'history' },
            ]}
            value={activeTab}
            onChange={setActiveTab}
            block
            className={styles.segmentedTabs}
          />
        </div>

        {activeTab === 'notes' && (
          <div className={styles.notesTab}>
            <div className={styles.addNoteSection}>
              <Flex gap="middle" align="flex-start">
                <TextArea
                  placeholder="Add a new note..."
                  value={newNote}
                  onChange={(e) => setNewNote(e.target.value)}
                  className={styles.noteTextarea}
                  rows={6}
                />
                <Button type="primary" loading={loading} onClick={handleAddNote} className={styles.addNoteButton}>
                  Add Note
                </Button>
              </Flex>
            </div>

            <div className={styles.notesList}>
              {Array.isArray(notes) && notes.length === 0 ? (
                <div className={styles.noNotes}>No Notes</div>
              ) : (
                notes?.map((note) => (
                  <div key={note.id} className={styles.noteItem}>
                    <div className={styles.noteHeader}>
                      <div className={styles.noteAuthor}>{note.user_id}</div>
                      <div className={styles.noteDate}>{formatDate(note.created_at)}</div>
                    </div>
                    <div className={styles.noteContent}>{note.note}</div>
                  </div>
                ))
              )}
            </div>
          </div>
        )}

        {activeTab === 'history' && (
          <div className={styles.historyTab}>
            <div className={styles.historyList}>
              {history?.map((note) => (
                <div key={note.id} className={styles.historyItem}>
                  <div className={styles.historyHeader}>
                    <div className={styles.historySource}>System</div>
                    <div className={styles.historyDate}>{formatDate(note.created_at)}</div>
                  </div>
                  <div className={styles.historyAction}>
                    Campaign: {note.campaign}{' '}
                    <span className="font-normal">
                      {' '}
                      sent {note.type === 'email' ? 'an ' : 'a '}
                      {note.type}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </Flex>
    </Modal>
  );
};

LeadHistoryModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
};
